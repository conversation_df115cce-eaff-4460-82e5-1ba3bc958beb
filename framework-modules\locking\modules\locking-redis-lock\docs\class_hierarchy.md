# Redis Locking Module: Class Hierarchy

## 1. Introduction

This document outlines the comprehensive class hierarchy for the `locking-redis-lock` module. The hierarchy supports all documented lock types, adhering to the **Async-First approach (utilizing Virtual Threads)** and aligning with standard Java patterns and Destilink Framework guidelines.


## 2. Core Design Principles

*   **Async-First with Virtual Threads**: Core operations are asynchronous, returning `CompletableFuture`s, executed on Virtual Threads. Synchronous `java.util.concurrent.locks.Lock` methods serve as blocking wrappers.
*   **Standard Java Compliance**: Integrates with `java.util.concurrent.locks.Lock` and `ReadWriteLock`.
*   **Clear Abstraction**: `AbstractRedisLock` encapsulates common Redis interaction logic.
*   **Modularity**: Specific lock types are concrete implementations.
*   **Configuration Driven**: Behavior influenced by `RedisLockProperties` (including `WatchdogProperties`, `Defaults`) and builder configurations. Watchdog eligibility depends on `LockOwnerSupplier.canUseWatchdog()` and `leaseTime > safetyBufferMillis`.
*   **Robust Error Handling**: Exceptions extend `AbstractRedisLockException`.

## 3. Class Hierarchy Diagram

```mermaid
classDiagram
    direction TB

    class Lock {
        <<interface>>
        +lock()
        +tryLock()
        +unlock()
        +newCondition()
    }

    class ReadWriteLock {
        <<interface>>
        +readLock() Lock
        +writeLock() Lock
    }

    class AsyncLock {
        <<interface>>
        +lockAsync() CompletableFuture
        +tryLockAsync() CompletableFuture
        +tryLockAsync(long, TimeUnit) CompletableFuture
        +unlockAsync() CompletableFuture
    }
    AsyncLock --|> Lock

    class AsyncReadWriteLock {
        <<interface>>
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    AsyncReadWriteLock --|> ReadWriteLock

    class AbstractRedisLock {
        <<Abstract>>
        #String lockKeyWithTypeSegment
        #String ownerId
        #RedisLockOperations redisLockOperations
        #RedisLockProperties redisLockProperties
        #LockOwnerSupplier lockOwnerSupplier
        #LockSemaphoreHolder lockSemaphoreHolder
        #ExecutorService virtualThreadExecutor
        +lockAsync() CompletableFuture
        +tryLockAsync() CompletableFuture
        +unlockAsync() CompletableFuture
        +lock() // blocking wrapper
        +tryLock() // blocking wrapper
        +unlock() // blocking wrapper
        #<i>Registers LockSemaphoreHolder before 1st Redis attempt</i>
        #<i>Handles MDC propagation to Virtual Threads</i>
    }
    AbstractRedisLock ..|> AsyncLock

    class RedisReentrantLock {
        // Uses 'reentrant' lock-type segment in keys
    }
    RedisReentrantLock --|> AbstractRedisLock

    class RedisStateLock {
        // Uses 'state' lock-type segment in keys
    }
    RedisStateLock --|> AbstractRedisLock

    class RedisStampedLock {
        // Uses 'stamped' lock-type segment in keys
    }
    RedisStampedLock --|> AbstractRedisLock

    class RedisReadWriteLock {
        // Uses 'readwrite' lock-type segment in keys
        -AsyncLock readLockInstance
        -AsyncLock writeLockInstance
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    RedisReadWriteLock ..|> AsyncReadWriteLock

    class RedisReadLock {
        // Part of RedisReadWriteLock
    }
    RedisReadLock --|> AbstractRedisLock

    class RedisWriteLock {
        // Part of RedisReadWriteLock
    }
    RedisWriteLock --|> AbstractRedisLock

    RedisReadWriteLock *-- RedisReadLock : creates
    RedisReadWriteLock *-- RedisWriteLock : creates
```

## 4. Interface and Class Descriptions

### 4.2. Custom Asynchronous Lock Interfaces

* **`com.tui.destilink.framework.locking.redis.lock.AsyncLock`**
  * **Extends:** `java.util.concurrent.locks.Lock`
  * **Purpose:** Defines contract for asynchronous lock operations executed on Virtual Threads. Methods return `CompletableFuture`s.

### 4.3. Abstract Base Class

* **`com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`**
  * **Implements:** `com.tui.destilink.framework.locking.redis.lock.AsyncLock`
  * **Key Responsibilities:**
    * Manages common fields, including `lockKey` (which incorporates the lock-type segment), `ownerId`, injected services, and the Virtual Thread `ExecutorService`.
    * Implements core async lock/unlock logic on Virtual Threads, delegating to `RedisLockOperations`.
    * Synchronous `Lock` methods are blocking wrappers.
    * Handles unlock messaging via `LockSemaphoreHolder` (registered *before* first Redis attempt).
    * Manages interactions with `LockWatchdog` (eligibility: `canUseWatchdog()` and `userLeaseTime > safetyBufferMillis`).
    * Constructs Redis keys with mandatory lock-type segments.
    * Handles MDC propagation to Virtual Threads.

### 4.4. Concrete Redis Lock Implementations

All reside in `com.tui.destilink.framework.locking.redis.lock.impl` and extend `AbstractRedisLock`. Their Redis keys include a specific lock-type segment.

* **`RedisReentrantLock`**: Primary reentrant lock. Uses `reentrant` lock-type segment.
* **`RedisStateLock`**: Specialized lock with associated state. Uses `state` lock-type segment.
* **`RedisStampedLock`**: Distributed stamped lock. Uses `stamped` lock-type segment.
* **`RedisReadWriteLock`**: Composed of `RedisReadLock` and `RedisWriteLock`. Uses `readwrite` lock-type segment for its main keys and `Individual Read Lock Timeout Key`s.
* **`RedisReadLock` / `RedisWriteLock`**: Components of `RedisReadWriteLock`.

## 5. "Application-Instance-Bound Lock" Characteristic

This is a behavioral characteristic, not a distinct class. Activated if:
1.  `LockOwnerSupplier.canUseWatchdog()` returns true.
2.  The lock's `userProvidedLeaseTime` (a `relativeLeaseTimeMillis`) is greater than `safetyBufferMillis` (calculated from `watchdog.interval * watchdog.factor`).
If met, `AbstractRedisLock` registers the lock with `LockWatchdog`. The watchdog then manages its lease using `PEXPIREAT`, respecting the `originalLeaseTimeMillis` stored in Redis.