# Redis Locking Module: Final Lock Acquisition Mechanism

## 1. Introduction

This document describes the final, consolidated process by which locks are acquired in the `locking-redis-lock` module. The mechanism is designed for efficiency and reliability, primarily relying on Redis Pub/Sub for unlock notifications to minimize polling, while incorporating a fallback mechanism and ensuring adherence to the `java.util.concurrent.Lock` interface contract.

The core logic is implemented in `AbstractRedisLock.java` and utilizes `UnlockMessageListener.java` (managed by `UnlockMessageListenerManager.java`) and `LockSemaphoreHolder.java`. All Redis interactions for lock acquisition are handled **asynchronously** via `RedisLockOperations` (which uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java`) and executed on **Virtual Threads**.

## 2. Core Principles

*   **Async-First Operations on Virtual Threads**: All lock acquisition attempts are fundamentally asynchronous, returning `CompletableFuture`s and executed on Virtual Threads. Synchronous `lock()` and `tryLock()` methods are blocking wrappers around these asynchronous calls.
*   **MDC Propagation**: MDC context is propagated to Virtual Threads executing lock logic.
*   **Non-Polling Preference**: `CompletableFuture`s associated with waiting tasks primarily wait passively for an unlock notification via Redis Pub/Sub.
*   **Atomic Operations**: Lock acquisition attempts are performed atomically using Lua scripts, executed asynchronously via `ClusterCommandExecutor`.
*   **`LockSemaphoreHolder` Registration First**: To prevent race conditions with missed Pub/Sub messages, the `LockSemaphoreHolder` (or its `CompletableFuture` equivalent for the current attempt) **must be obtained/created and registered with `UnlockMessageListenerManager` *before* the first Redis attempt to acquire the lock.**
*   **Lua Script Return Values**: Acquisition Lua scripts return a status code (`<0` for acquired, `>=0` for held by other with TTL/timestamp), `expiresAtMillis`, and `originalLeaseTimeMillis`.
*   **TTL-Aware Fallback**: If a Pub/Sub notification is missed or delayed, the waiting mechanism also times out based on `min(Returned TTL from script, Configured defaults.retryInterval)`. Upon timeout, a new lock acquisition attempt is made. `Thread.sleep()` is used for `defaults.retryInterval` delays on Virtual Threads.
*   **`acquireTimeout` Governs Overall Attempt**: The `defaults.acquireTimeout` property provides an approximate overall time limit for `tryLock` operations.
    *   **Non-Interruption of In-Flight Redis Operations**: `acquireTimeout` MUST NOT interrupt a Redis command already dispatched to `ClusterCommandExecutor`.
    *   **Redis Operation Result Precedence**: The result (success, failure, or specific exception) of an in-flight Redis operation takes precedence over a concurrently expiring `acquireTimeout`.
*   **`maxRetries` for Individual Redis Ops**: The `defaults.maxRetries` applies to retrying *individual failing Redis operations* within `RedisLockOperationsImpl`, not to the overall loop for acquiring a busy lock.
*   **Lightweight Lock Instances**: Designed for efficiency with shared components.

## 3. Lock Acquisition Scenarios

### 3.1. Synchronous Wait Scenario (`lock.lock()`) - Wrapper Implementation

This method acts as a blocking wrapper around the asynchronous acquisition logic, executed on a Virtual Thread.

```mermaid
flowchart TD
    A["Start: lock.lock() (Synchronous Wrapper)"] --> B["Dispatch to Virtual Thread: Call lockAsync()"]
    B --> C["Block on CompletableFuture.get() from lockAsync() (Platform thread parks, Virtual Thread runs/parks)"]
    C --> D{"CompletableFuture Completed?"}
    D -- "Yes (Lock Acquired)" --> E["Return"]
    D -- "Exceptional Completion (e.g., LockAcquisitionException, LockTimeoutException from acquireTimeout)" --> F["Unwrap Exception and Throw"]
    D -- "InterruptedException caught during get()" --> G["Re-assert Thread.interrupted() / Throw LockInterruptedException"]

    subgraph AsynchronousLockAcquisitionOnVirtualThread ["Internal Asynchronous Lock Acquisition (lockAsync() on Virtual Thread)"]
        ALA1["Generate Unique Request ID (requestUuid) for this logical lockAsync() call by RedisLockOperations"]
        ALA1 --> ALA1_5["Get/Create LockSemaphoreHolder for lockKey & Register with UnlockMessageListenerManager (BEFORE first Redis attempt)"]
        ALA1_5 --> ALA2["Loop (Until Lock Acquired or Interrupted)"]
        ALA2 --> ALA3["Call tryAcquireLockInternalAsync (Executes Lua script via RedisLockOperations - uses ClusterCommandExecutor). RedisLockOperations may retry individual Redis command failures up to defaults.maxRetries."]
        ALA3 --> ALA4{"Lua Script Result (status, expiresAtMillis, originalLeaseTimeMillis)?"}
        ALA4 -- "Lock Acquired (status < 0)" --> ALA5["Register with LockWatchdog (if eligible, using originalLeaseTimeMillis and expiresAtMillis)"]
        ALA5 --> ALA6["Complete lockAsync() CompletableFuture successfully"]
        ALA4 -- "Lock Held by Other (status >= 0, provides current lock's PTTL/expiresAt)" --> ALA7["Calculate Wait Duration: min(Returned PTTL/time_to_expiry, defaults.retryInterval)"]
        ALA7 --> ALA8["Wait on LockSemaphoreHolder's CompletableFuture for signal or Wait Duration to elapse (Virtual Thread parks using Thread.sleep() for retryInterval or waits on CompletableFuture from semaphore)"]
        ALA8 -- "Signal Received (Pub/Sub)" --> ALA2
        ALA8 -- "Wait Duration Elapsed (Timeout)" --> ALA2
        ALA8 -- "Redis-related Exception from ALA3" --> ALA9["Complete lockAsync() CompletableFuture exceptionally"]
    end
    B --> ALA1
```

**Process Description (Synchronous Wrapper `lock()`):**

1.  **Dispatch to Virtual Thread**: `AbstractRedisLock.lock()` dispatches the call to `lockAsync()` to be executed on a Virtual Thread.
2.  **Block and Wait**: The calling platform thread blocks by calling `CompletableFuture.get()` on the future returned by `lockAsync()`.
3.  **Handle Completion**: As per the diagram, handling success, specific lock exceptions, or interruption.

### 3.2. Asynchronous Acquisition (`lockAsync()`) - Primary Flow on Virtual Thread

This describes the core, non-blocking asynchronous lock acquisition, executed on a Virtual Thread.

```mermaid
flowchart TD
    A["Start: lockAsync() (Primary API, executes on Virtual Thread)"] --> B["RedisLockOperations generates Unique Request ID (requestUuid) for this logical call"]
    B --> C["Create new CompletableFuture<Void> (lockFuture) to be returned to user"]
    C --> C_REG["Get/Create LockSemaphoreHolder for lockKey & Register with UnlockMessageListenerManager (BEFORE first Redis attempt)"]
    C_REG --> D["Recursive Attempt Function/Loop (tryAcquireLoop)"]
    D --> E["Call tryAcquireLockInternalAsync (Executes Lua script via RedisLockOperations - uses ClusterCommandExecutor). RedisLockOperations may retry individual Redis command failures up to defaults.maxRetries."]
    E --> F{"Lua Script Result (status, expiresAtMillis, originalLeaseTimeMillis)?"}
    F -- "Success (Lock Acquired, status < 0)" --> G["Register with LockWatchdog (if eligible, using originalLeaseTimeMillis and expiresAtMillis)"]
    G --> H["lockFuture.complete(null)"]
    H --> I["Return lockFuture to user (completed)"]
    F -- "Failure (Lock Held by Other, status >= 0, provides current lock's PTTL/expiresAt)" --> J["Calculate Wait Duration: min(Returned PTTL/time_to_expiry, defaults.retryInterval)"]
    J --> K["Associate lockFuture with LockSemaphoreHolder (to be completed on signal or timeout)"]
    K --> L["Return lockFuture to user (still incomplete, waiting for signal/retry)"]
    F -- "Error from E (e.g., Redis connection error after retries)" --> M["lockFuture.completeExceptionally(exception)"]
    M --> I
```

**Process Description (Asynchronous `lockAsync()` on Virtual Thread):**

1.  **Initiate & Create Future**: `AbstractRedisLock.lockAsync()` is called (runs on a VT). A `CompletableFuture<Void>` (`lockFuture`) is created.
2.  **Register Semaphore Holder**: `LockSemaphoreHolder` is obtained/created and registered with `UnlockMessageListenerManager` *before* the first Redis attempt.
3.  **Attempt Atomic Acquisition (Asynchronously via `RedisLockOperations`):**
    *   `RedisLockOperations` generates `requestUuid`.
    *   `tryAcquireLockInternalAsync()` invokes the Lua script. `RedisLockOperations` handles internal retries for this Redis command if it fails transiently.
4.  **Handle Script Result (Chained)**:
    *   **Success**: Lock acquired. Register with watchdog if eligible. `lockFuture` is completed.
    *   **Lock Held**: Calculate wait time (`min(TTL, defaults.retryInterval)`). `lockFuture` is associated with `LockSemaphoreHolder` to wait for Pub/Sub signal or this calculated timeout.
    *   **Error**: `lockFuture` is completed exceptionally.
5.  **Return Future**: `lockFuture` is returned to the caller immediately (may be incomplete).
6.  **Completion by `UnlockMessageListener`**: If waiting, when `UnlockMessageListener` receives a Pub/Sub message, it signals `LockSemaphoreHolder`, which then typically triggers a re-attempt (re-enters the loop D) for the associated `lockFuture`.

### 3.3. Timeout Scenario (`tryLockAsync(long time, TimeUnit unit)`) - Primary Flow on Virtual Thread

This flow is similar to `lockAsync()`, but respects an overall `defaults.acquireTimeout` (derived from `time`, `unit`).

```mermaid
flowchart TD
    A[Start: tryLockAsync(timeout, unit) on VT] --> B[Calculate Deadline (startTime + timeout from defaults.acquireTimeout)]
    B --> C[Create new CompletableFuture<Boolean> (tryLockFuture)]
    C --> C_REG["Get/Create LockSemaphoreHolder for lockKey & Register with UnlockMessageListenerManager (BEFORE first Redis attempt)"]
    C_REG --> D["Recursive Attempt Function/Loop (tryAcquireLoopWithTimeout)"]
    D --> E["Check: Is currentTime < Deadline?"]
    E -- No (Deadline Reached) --> L[tryLockFuture.complete(false)]
    E -- Yes --> F["Call tryAcquireLockInternalAsync (Lua via RedisLockOperations). RLOps handles its internal retries."]
    F --> G{"Lua Script Result (status, expiresAtMillis, originalLeaseTimeMillis)?"}
    G -- Success (Lock Acquired, status < 0) --> H["Register with LockWatchdog (if eligible)"]
    H --> I["tryLockFuture.complete(true)"]
    I --> X[Return tryLockFuture to user (completed)]

    G -- Failure (Lock Held by Other, status >= 0, provides PTTL/expiresAt) --> J["Check: Is currentTime < Deadline? (Re-check after Redis call)"]
    J -- No (Deadline Reached) --> L
    J -- Yes --> K["Calculate Wait Duration: min(Returned PTTL/time_to_expiry, defaults.retryInterval, Remaining Overall Timeout until Deadline)"]
    K -- "Wait Duration <= 0" --> L
    K -- "Wait Duration > 0" --> M["Associate tryLockFuture with LockSemaphoreHolder for signal or Wait Duration"]
    M --> N["Return tryLockFuture to user (still incomplete, waiting)"]

    G -- "Error from F (e.g., Redis connection error after RLOps retries)" --> O["tryLockFuture.completeExceptionally(exception)"]
    O --> X
    L --> X
```

**Process Description (Asynchronous `tryLockAsync()` on Virtual Thread):**

1.  **Initiate, Deadline, Future**: `AbstractRedisLock.tryLockAsync()` calculates `deadline` based on `defaults.acquireTimeout`. A `CompletableFuture<Boolean>` (`tryLockFuture`) is created.
2.  **Register Semaphore Holder**: As in `lockAsync()`.
3.  **Attempt Loop with Timeout Check**:
    *   Before each Redis attempt, check if `currentTime < deadline`. If not, complete `tryLockFuture` with `false`.
    *   Attempt atomic acquisition as in `lockAsync()`.
4.  **Handle Script Result & Check Deadline**:
    *   **Success**: `tryLockFuture` completes with `true`.
    *   **Lock Held**: Re-check `currentTime < deadline`. If deadline met, complete `false`. Otherwise, calculate wait duration considering remaining time until deadline. If wait duration is `<=0`, complete `false`. Else, associate with `LockSemaphoreHolder`.
    *   **Error**: `tryLockFuture` completes exceptionally.
    *   **`acquireTimeout` Nuances**: If `acquireTimeout` expires *during* an in-flight Redis command, the command completes. If it acquired the lock, `tryLockFuture` is `true`. If it failed with a Redis error, that error is propagated. `tryLockFuture` is only `false` due to timeout if the deadline passes *between* Redis attempts or after a non-acquiring, non-erroring Redis attempt.

## 4. Key Components Involved

*   **`AbstractRedisLock`**: Orchestrates acquisition on Virtual Threads, delegates to `RedisLockOperations`, manages `LockSemaphoreHolder` interaction.
*   **`RedisLockOperations`**: Executes Lua scripts via `ClusterCommandExecutor`, generates `requestUuid`, handles internal retries for individual Redis ops, parses structured Lua responses.
*   **`UnlockMessageListenerManager` / `UnlockMessageListener`**: Manage Pub/Sub, signal `LockSemaphoreHolder`.
*   **`LockSemaphoreHolder`**: Manages `CompletableFuture`s for waiting tasks.
*   **Lua Scripts**: Perform atomic operations, return structured status/`expiresAtMillis`/`originalLeaseTimeMillis`.
*   **Virtual Thread Executor**: Executes all lock logic.