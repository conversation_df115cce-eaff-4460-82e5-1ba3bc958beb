# Redis Locking Module: Final Implementation Details

## 1. Introduction

This document provides detailed implementation specifics for key components and mechanisms within the consolidated `locking-redis-lock` module. Understanding these details is crucial for developing, debugging, and effectively utilizing the module. This plan synthesizes the core implementation aspects from previous planning phases, with a strong emphasis on the **Async-First approach (utilizing Virtual Threads)**, mandatory use of `redis-core` components, **centralized idempotency**, **refined watchdog logic**, **lock-type specific key schemas**, and strict adherence to framework guidelines.

**Crucially, all Redis keys, including those with hashtags, MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `com.tui.destilink.framework.redis.core.key.RedisKey`, and `com.tui.destilink.framework.redis.core.key.RedisKeyPrefix` from the `redis-core` module. These keys MUST include a mandatory lock-type segment (e.g., `reentrant`, `stamped`) for semantic isolation.**

## 2. Core Components and Responsibilities

The module is structured around several core components that collaborate to provide distributed locking functionality:

* `RedisLockAutoConfiguration` (Spring `@AutoConfiguration`):
  * The module's entry point for Spring Boot.
  * **Strictly adheres to no `@ComponentScan`**. All framework beans are explicitly defined via `@Bean` methods.
  * Conditionally enables the locking module based on the `destilink.fw.locking.redis.enabled` property.
  * Responsible for instantiating and wiring all shared service beans (including a Virtual Thread `ExecutorService`) and the `LockBucketRegistry`.
  * Registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.

* `LockComponentRegistry` (Spring Bean):
  * A central registry injected into lock builders and other components.
  * Provides access to shared services: `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, an `ObjectProvider<LockMonitor>`, and the Virtual Thread `ExecutorService`.
  * Simplifies dependency management for lock instances and internal services.

* `RedisLockProperties` (Spring `@ConfigurationProperties`):
  * Binds global configurations from YAML files (e.g., `destilink.fw.locking.redis.*`).
  * Contains nested `WatchdogProperties` (for system-level watchdog config like `interval`, `factor`) and `Defaults` (for overridable lock implementation defaults like `leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`).
  * Also holds top-level properties like `enabled`, `stateKeyExpiration`, and `responseCacheTtl` (critical for idempotency).
  * **Removed properties**: `pubSubWaitTimeout`, `lockOwnerIdValidationRegex`, `maxLockNameLength`, `maxBucketNameLength`, `maxScopeLength`. `fairLockBehavior`, `asyncExecutorName`, `redisOperationTimeout` (from `Defaults`).
  * Works in conjunction with `com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java` to ensure consistent Redis client configuration.

* `LockBucketConfig` (Non-Spring managed POJO):
  * Holds the *resolved* and *effective* configuration for a specific lock bucket.
  * Instantiated by `LockBucketBuilder`, initialized with global defaults from `RedisLockProperties.Defaults` and potentially from `RedisCoreProperties`.
  * Bucket-level defaults can be overridden programmatically. Watchdog eligibility for locks within the bucket is determined by the configured `LockOwnerSupplier` and other global/instance conditions.

* `LockBucketRegistry` (Spring Bean):
  * The primary factory for initiating the lock creation process.
  * Provides the entry point to the fluent builder API.
  * Injects the `LockComponentRegistry` and an initialized `LockBucketConfig` into the `LockBucketBuilder`.

* Builder Chain (`LockBucketBuilder` -> `LockConfigBuilder` -> `AbstractLockTypeConfigBuilder` & Subclasses):
  * Configures bucket-name, scope, custom `LockOwnerSupplier`, and overrides for `defaults.leaseTime`, `defaults.retryInterval`, `defaults.maxRetries`, `defaults.acquireTimeout`, and `stateKeyExpiration`. The `LockOwnerSupplier` (via `canUseWatchdog()`) and `leaseTime` (relative to `safetyBufferMillis`) are key to determining watchdog eligibility.

* `AbstractRedisLock` (Base Class for Locks):
  * Provides common asynchronous functionality executed on Virtual Threads: `lockAsync()`, `tryLockAsync()`, `unlockAsync()`.
  * **Synchronous `lock()`, `tryLock()`, and `unlock()` methods from `java.util.concurrent.locks.Lock` are implemented as blocking wrappers around these asynchronous operations.**
  * Manages the core lock acquisition/release lifecycle, including interaction with `LockSemaphoreHolder` (via `CompletableFuture`s) for waiting and `LockWatchdog` for lease extension.
  * Delegates Redis script execution to `RedisLockOperations`.
  * Defines abstract asynchronous methods like `tryAcquireLockScriptAsync()`, `releaseLockScriptAsync()` to be implemented by concrete lock types.
  * Handles MDC propagation to Virtual Threads.
  * Obtains/creates `LockSemaphoreHolder` and registers it with `UnlockMessageListenerManager` *before* the first Redis acquisition attempt.

* Concrete Lock Implementations (e.g., `RedisReentrantLock`, `RedisStateLock`, `RedisReadWriteLock`, `RedisStampedLock`):
  * Extend `AbstractRedisLock` and **MUST implement the `AsyncLock` interface**.
  * Implement abstract asynchronous methods to provide specific Lua script names and arguments, utilizing `CompletableFuture`s.
  * Keys used by these locks MUST include their specific lock-type segment (e.g., `reentrant`, `state`, `readwrite`, `stamped`).
  * **`RedisReentrantLock`**: Manages reentrancy count and owner information within a **Redis Hash** associated with the `lockKey` (which includes the `reentrant` type segment).
  * **`RedisReadWriteLock`**: Contains inner `ReadLock` and `WriteLock`. These inner locks extend `AbstractRedisLock` and implement `AsyncLock`. Manages its state in a main Hash (`lockKey` with `readwrite` type segment) and uses `Individual Read Lock Timeout Key`s (also with `readwrite` type segment).

* `ScriptLoader` (Spring Bean):
  * Loads all necessary Lua scripts from the classpath during application startup.
  * Caches scripts for efficient reuse.

* `RedisLockOperations` (Spring Bean, typically `RedisLockOperationsImpl`):
  * Abstraction layer for Redis communication specific to lock operations.
  * **CRITICAL: Exclusively uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all Redis command executions and Lua script invocations.**
  * **Centralized Idempotency Management**:
    *   Generates a unique `requestUuid` for each logical lock operation (e.g., a single `tryLock` call).
    *   Uses the *same `requestUuid`* for all internal retries (governed by `defaults.maxRetries` and `defaults.retryInterval`) of that single logical operation.
    *   Passes `requestUuid` and `responseCacheTtl` to all mutating Lua scripts.
  * **Lua Script Return Handling**: Parses structured responses from Lua scripts (status, `expiresAtMillis`, `originalLeaseTimeMillis`).
  * **Internal Retries**: Implements retry logic for individual Redis operations based on `defaults.maxRetries` and `defaults.retryInterval` for `RetryableLockException` types.

* `DefaultLockOwnerSupplier` (Spring Bean):
  * Provides the default mechanism for generating unique `ownerId`s. Implements `canUseWatchdog()` for watchdog eligibility checks.

* `RedisLockErrorHandler` (Spring Bean):
  * Centralizes logic for interpreting exceptions from Redis operations, including those from `ClusterCommandExecutor`.
  * Translates low-level exceptions into specific `AbstractRedisLockException` subtypes.

* `UnlockMessageListenerManager` (Spring Bean):
  * Manages the lifecycle of `UnlockMessageListener` instances.

* `UnlockMessageListener` (Per Bucket, managed by Manager):
  * Implements `org.springframework.data.redis.connection.MessageListener`. Each instance subscribes to a pattern for its bucket-specific Pub/Sub channels (e.g., `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`).
  * Derives `lockName` and `lockType` from the *channel name*. Parses `UnlockType` from the message payload.
  * Signals the `LockSemaphoreHolder` (completes its `CompletableFuture`).
  * Message processing is done on an optimized executor (e.g., Virtual Threads).

* `LockSemaphoreHolder` (Non-Spring managed, per `lockKey`):
  * An instance is associated with each `lockKey` that has waiting `CompletableFuture`s, managed *within* the per-bucket `UnlockMessageListener`.
  * Facilitates the completion of associated `CompletableFuture`s when an unlock notification is received.

* `LockWatchdog` (Spring Bean):
  * **Always active** if the module is enabled.
  * Background mechanism for automatic lease extension of active, application-instance-bound locks whose `userProvidedLeaseTime > safetyBufferMillis`.
  * **Periodically executes `watchdog_refresh_lock.lua` script using `RedisLockOperations` (asynchronously).**
  * Uses `watchdog.interval` for scheduling and `watchdog.factor` to calculate `safetyBufferMillis`.
  * Manages `expiresAtMillis` in Redis using `PEXPIREAT`, preserving the `originalLeaseTimeMillis` stored in `lockDataKey`.

* `LockMonitor` (Spring Bean, Optional):
  * Collects and exposes metrics related to lock operations using Micrometer.

### 2.A RedisReadWriteLock

*   **Key Characteristics:**
    *   Inner `ReadLock` and `WriteLock` extend `AbstractRedisLock` and implement `AsyncLock`.
    *   Reentrancy for reads and writes managed in a main Redis Hash (`lockKey` with `readwrite` type segment).
    *   Uses `Individual Read Lock Timeout Key`s (Redis Strings with TTL, also with `readwrite` type segment) for each reentrant read acquisition.
    *   Main lock Hash's TTL dynamically managed by Lua scripts based on its own lease (potentially watchdog-managed to `safetyBufferMillis` or `userIntendedExpireTimeMillis`) and active individual read lock timeouts.
*   **Operational Details (Async-First with Virtual Threads):**
    *   `tryAcquireLockAsync` for read/write locks calls respective Lua scripts (`try_read_lock.lua`, `try_write_lock.lua`) via `RedisLockOperations`.
    *   Lua scripts manage reentrancy counts, `mode`, `ownerId` in the main Hash, create/delete individual read timeout keys, and update `expiresAtMillis` and `originalLeaseTimeMillis` in `lockDataKey`.
    *   Watchdog can manage the main `RedisReadWriteLock` instance if eligible.

### 2.B RedisStampedLock

*   **Key Characteristics:**
    *   Uses a Redis Hash (`lockKey` with `stamped` type segment) to store `version`, `write_owner_id`, `read_holders`, etc.
    *   String-based stamps.
    *   Asynchronous operations for write, read, optimistic read, and conversions, all via Lua scripts and `RedisLockOperations`.
    *   Watchdog can manage write locks if eligible.

### 2.X Asynchronous Lock Operations (`AsyncLock` Interface)

*   **Purpose**: Provides non-blocking API returning `CompletableFuture`s, executed on Virtual Threads.
*   **Integration**: All concrete lock implementations implement `AsyncLock`.
*   **Waiting Mechanism**: For `lockAsync()`, `CompletableFuture`s are registered with `LockSemaphoreHolder` and completed by `UnlockMessageListener` or timeout.

## 3. Key Mechanisms Detailed

* **Lock Acquisition and Waiting**: See [Lock Acquisition Mechanism](lock_acquisition.md). Core flow is asynchronous on Virtual Threads, `LockSemaphoreHolder` registered *before* first Redis attempt, `acquireTimeout` respected with nuances.
* **Lock Release and Messaging**: See [Unlock Messaging](messaging.md). Lua scripts publish `UnlockType`, listener derives `lockName` and `lockType` from channel.
* **Lease Extension (Watchdog)**: See [Watchdog Mechanism](watchdog.md). Always active service, conditional monitoring, `PEXPIREAT`, `safetyBufferMillis`, `originalLeaseTimeMillis` preservation.
* **Exception Handling**: See [Exception Handling](exception_handling.md). `RedisLockErrorHandler` translates, exceptions carry context.
* **Centralized Idempotency**: `RedisLockOperationsImpl` generates `requestUuid` per logical operation (same for its internal retries). Lua scripts use this with `responseCacheTtl` to check/store results in a response cache. This is mandatory for all mutating scripts.
* **Configuration Loading**: `RedisLockProperties` (with `WatchdogProperties` and `Defaults` nested) for global, builder chain for programmatic overrides.
* **Lua-Only Redis Operations**: All lock state/TTL/metadata changes are exclusively via Lua scripts.

### 3.1 Centralized Idempotency Flow

```mermaid
flowchart TD
    A[Client Initiates Lock Operation] --> B[RedisLockOperationsImpl]
    B --> C[Generate Unique requestUuid for Logical Operation (used for all its internal retries)]
    C --> D[Construct Response Cache Key:<br/>&lt;prefix&gt;:&lt;bucketName&gt;:__resp_cache__:<lockType>:{&lt;lockName&gt;}:&lt;requestUuid&gt;]
    D --> E[Call Lua Script with requestUuid and responseCacheTtl]

    subgraph LuaScriptExecution [Lua Script Execution on Redis Server]
        LS1[Receive requestUuid, responseCacheTtl, and operation parameters] --> LS2{Check Response Cache for requestUuid?}
        LS2 -- "Cache Hit: Result Found" --> LS3[Return Cached Result<br/>Operation Already Completed]
        LS2 -- "Cache Miss: No Result Found" --> LS4[Execute Core Lock Operation Logic<br>(Reads/writes lockKey, lockDataKey)]
        LS4 --> LS5{Operation Successful?}
        LS5 -- "Success" --> LS6[Store Operation Result (status, expiresAtMillis, originalLeaseTimeMillis) in Response Cache<br/>Key: requestUuid, TTL: responseCacheTtl]
        LS6 --> LS7[Return Operation Result]
        LS5 -- "Failure" --> LS8[Return Error Result<br/>Do Not Cache Failures]
    end

    E --> LS1
    LS3 --> F[Return Cached Result to Client<br/>Idempotent Behavior Achieved]
    LS7 --> G[Return Fresh Result to Client<br/>Operation Completed Successfully]
    LS8 --> H[Return Error to Client<br/>Operation Failed, Can Be Retried by RedisLockOperationsImpl or propagated]

    subgraph RetryScenario [RedisLockOperationsImpl Internal Retry Scenario for a single logical op]
        I[Network Failure/Timeout During Initial Redis Command] --> J[RedisLockOperationsImpl Retries with Same requestUuid (up to defaults.maxRetries)]
        J --> B
    end
```

**Implementation Details:**
*   `RedisLockOperationsImpl` generates `requestUuid` per logical public call. This same `requestUuid` is used for internal retries of individual Redis commands for that call.
*   Response cache keys include the `lockType` segment.
*   Lua scripts store structured results (status, `expiresAtMillis`, `originalLeaseTimeMillis`) in the response cache.
